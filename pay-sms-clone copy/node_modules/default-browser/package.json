{"name": "default-browser", "version": "5.2.1", "description": "Get the default browser", "license": "MIT", "repository": "sindresorhus/default-browser", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "windows.js"], "keywords": ["macos", "linux", "browser", "default", "plist", "web", "bundle", "bundleid", "id", "identifier", "uti", "cf<PERSON><PERSON><PERSON><PERSON>", "applescript"], "dependencies": {"bundle-name": "^4.1.0", "default-browser-id": "^5.0.0"}, "devDependencies": {"ava": "^6.0.1", "tsd": "^0.30.0", "xo": "^0.56.0"}}